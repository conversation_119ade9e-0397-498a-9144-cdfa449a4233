from fastapi import <PERSON>AP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
import os
import sys
import uuid
import asyncio
import json
from typing import Optional
import shutil
from pathlib import Path

# Add the parent directory to the path to import our components
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Components.YoutubeDownloader import download_youtube_video
from Components.Edit import extractAudio, crop_video
from Components.Transcription import transcribeAudio
from Components.LanguageTasks import GetHighlight

app = FastAPI(title="AI YouTube Shorts Generator", version="2.0.0")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with your frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories for storing files
os.makedirs("uploads", exist_ok=True)
os.makedirs("outputs", exist_ok=True)
os.makedirs("temp", exist_ok=True)

# Mount static files
app.mount("/outputs", StaticFiles(directory="outputs"), name="outputs")

# Pydantic models
class VideoRequest(BaseModel):
    youtube_url: str

class JobStatus(BaseModel):
    job_id: str
    status: str  # "pending", "processing", "completed", "failed"
    progress: int  # 0-100
    message: str
    result_url: Optional[str] = None
    error: Optional[str] = None

# In-memory job storage (in production, use Redis or database)
jobs = {}

@app.get("/api")
async def root():
    return {"message": "AI YouTube Shorts Generator API", "version": "2.0.0"}

@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "lm_studio_configured": True}

@app.post("/api/process-video")
async def process_video(request: VideoRequest, background_tasks: BackgroundTasks):
    """Start processing a YouTube video to create shorts"""
    job_id = str(uuid.uuid4())
    
    # Initialize job status
    jobs[job_id] = JobStatus(
        job_id=job_id,
        status="pending",
        progress=0,
        message="Job created, starting processing..."
    )
    
    # Start background processing
    background_tasks.add_task(process_video_background, job_id, request.youtube_url)
    
    return {"job_id": job_id, "status": "started"}

@app.get("/api/job/{job_id}")
async def get_job_status(job_id: str):
    """Get the status of a processing job"""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return jobs[job_id]

@app.get("/download/{filename}")
async def download_file(filename: str):
    """Download a generated video file"""
    file_path = f"outputs/{filename}"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")
    
    return FileResponse(
        file_path,
        media_type="video/mp4",
        filename=filename
    )

async def process_video_background(job_id: str, youtube_url: str):
    """Background task to process the video"""
    try:
        # Update job status
        jobs[job_id].status = "processing"
        jobs[job_id].progress = 10
        jobs[job_id].message = "Downloading video from YouTube..."
        
        # Step 1: Download video
        video_path = download_youtube_video(youtube_url)
        if not video_path:
            raise Exception("Failed to download video")
        
        # Convert webm to mp4 if needed
        if video_path.endswith(".webm"):
            video_path = video_path.replace(".webm", ".mp4")
        
        jobs[job_id].progress = 30
        jobs[job_id].message = "Extracting audio..."
        
        # Step 2: Extract audio
        audio_path = extractAudio(video_path)
        if not audio_path:
            raise Exception("Failed to extract audio")
        
        jobs[job_id].progress = 50
        jobs[job_id].message = "Transcribing audio with Whisper..."
        
        # Step 3: Transcribe audio
        transcriptions = transcribeAudio(audio_path)
        if not transcriptions:
            raise Exception("Failed to transcribe audio")
        
        # Prepare transcription text
        trans_text = ""
        for text, start, end in transcriptions:
            trans_text += f"{start} - {end}: {text}\n"
        
        jobs[job_id].progress = 70
        jobs[job_id].message = "Analyzing content with LM Studio..."
        
        # Step 4: Get highlights using LM Studio
        start_time, end_time = GetHighlight(trans_text)
        if start_time == 0 and end_time == 0:
            raise Exception("Failed to identify highlights")
        
        jobs[job_id].progress = 85
        jobs[job_id].message = "Creating short video..."
        
        # Step 5: Create the short video (simplified version)
        output_filename = f"short_{job_id}.mp4"
        final_output = f"outputs/{output_filename}"
        
        # Crop the video to the highlight timeframe
        crop_video(video_path, final_output, start_time, end_time)
        
        jobs[job_id].progress = 100
        jobs[job_id].status = "completed"
        jobs[job_id].message = "Video processing completed successfully!"
        jobs[job_id].result_url = f"/download/{output_filename}"
        
        # Clean up temporary files
        cleanup_temp_files([video_path, audio_path])
        
    except Exception as e:
        jobs[job_id].status = "failed"
        jobs[job_id].error = str(e)
        jobs[job_id].message = f"Processing failed: {str(e)}"
        print(f"Error processing video for job {job_id}: {e}")

def cleanup_temp_files(file_paths):
    """Clean up temporary files"""
    for file_path in file_paths:
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"Warning: Could not remove temp file {file_path}: {e}")

# Mount the HTML frontend (this should be last to catch all remaining routes)
app.mount("/", StaticFiles(directory="frontend_html", html=True), name="frontend")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
