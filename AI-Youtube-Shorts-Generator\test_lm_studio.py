#!/usr/bin/env python3
"""
Test script to verify LM Studio connectivity and functionality
"""

import requests
import json
from dotenv import load_dotenv
import os

load_dotenv()

# LM Studio configuration
LM_STUDIO_BASE_URL = os.getenv("LM_STUDIO_BASE_URL", "http://localhost:1234")
LM_STUDIO_MODEL = os.getenv("LM_STUDIO_MODEL", "llama-3.2-8x3b-moe-dark-champion-instruct-uncensored-abliterated-18.4b")

def test_lm_studio_connection():
    """Test if LM Studio is running and accessible"""
    try:
        print("🔍 Testing LM Studio connection...")
        response = requests.get(f"{LM_STUDIO_BASE_URL}/v1/models", timeout=5)
        
        if response.status_code == 200:
            models = response.json()
            print("✅ LM Studio is running!")
            print(f"📋 Available models: {len(models.get('data', []))}")
            for model in models.get('data', []):
                print(f"   - {model.get('id', 'Unknown')}")
            return True
        else:
            print(f"❌ LM Studio responded with status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to LM Studio. Make sure it's running on http://localhost:1234")
        return False
    except Exception as e:
        print(f"❌ Error testing LM Studio: {e}")
        return False

def test_lm_studio_chat():
    """Test LM Studio chat completion functionality"""
    try:
        print("\n🤖 Testing LM Studio chat completion...")
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "model": LM_STUDIO_MODEL,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say hello and confirm you're working correctly. Keep it brief."}
            ],
            "temperature": 0.7,
            "max_tokens": 100,
            "stream": False
        }
        
        response = requests.post(
            f"{LM_STUDIO_BASE_URL}/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            message = result["choices"][0]["message"]["content"]
            print("✅ LM Studio chat completion working!")
            print(f"🤖 Response: {message}")
            return True
        else:
            print(f"❌ Chat completion failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing chat completion: {e}")
        return False

def test_highlight_detection():
    """Test the highlight detection functionality"""
    try:
        print("\n🎯 Testing highlight detection...")
        
        # Sample transcription for testing
        sample_transcription = """
        0.0 - 5.0: Welcome to this amazing tutorial about AI and machine learning
        5.0 - 15.0: Today we're going to explore some really exciting concepts
        15.0 - 25.0: This is the most important part where we discuss the breakthrough
        25.0 - 35.0: And here's another fascinating insight that will blow your mind
        35.0 - 45.0: Let's wrap up with some final thoughts and conclusions
        """
        
        from Components.LanguageTasks import GetHighlight
        
        start_time, end_time = GetHighlight(sample_transcription)
        
        if start_time != 0 or end_time != 0:
            print(f"✅ Highlight detection working!")
            print(f"🎬 Detected highlight: {start_time}s - {end_time}s")
            return True
        else:
            print("❌ Highlight detection failed - returned 0,0")
            return False
            
    except Exception as e:
        print(f"❌ Error testing highlight detection: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 AI YouTube Shorts Generator - LM Studio Test Suite")
    print("=" * 60)
    
    # Test 1: Connection
    connection_ok = test_lm_studio_connection()
    
    if not connection_ok:
        print("\n❌ Cannot proceed with further tests. Please start LM Studio first.")
        print("\n📝 Instructions:")
        print("1. Open LM Studio")
        print("2. Load the model: llama-3.2-8x3b-moe-dark-champion-instruct-uncensored-abliterated-18.4b")
        print("3. Start the local server (usually on port 1234)")
        print("4. Run this test again")
        return
    
    # Test 2: Chat completion
    chat_ok = test_lm_studio_chat()
    
    # Test 3: Highlight detection
    highlight_ok = test_highlight_detection()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   Connection: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    print(f"   Chat API: {'✅ PASS' if chat_ok else '❌ FAIL'}")
    print(f"   Highlight Detection: {'✅ PASS' if highlight_ok else '❌ FAIL'}")
    
    if all([connection_ok, chat_ok, highlight_ok]):
        print("\n🎉 All tests passed! Your system is ready to generate AI shorts!")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
