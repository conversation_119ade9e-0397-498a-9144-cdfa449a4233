# 🎬 AI YouTube Shorts Generator

Transform long YouTube videos into engaging shorts using AI-powered highlight detection with **LM Studio** and a beautiful modern web interface.

![AI Shorts Generator](https://img.shields.io/badge/AI-Shorts%20Generator-purple?style=for-the-badge)
![LM Studio](https://img.shields.io/badge/LM%20Studio-Powered-blue?style=for-the-badge)
![HTML/CSS/JS](https://img.shields.io/badge/Frontend-HTML%2FCSS%2FJS-orange?style=for-the-badge)
![FastAPI](https://img.shields.io/badge/FastAPI-Backend-green?style=for-the-badge)

## ✨ Features

- 🎥 **YouTube Integration** - Download and process videos from YouTube URLs
- 🤖 **LM Studio Powered** - Advanced AI analysis using your local LM Studio instance
- ✨ **Smart Highlights** - Automatically detect the most engaging moments
- 🎨 **Beautiful UI** - Modern, responsive web interface with stunning animations
- 🚀 **Fast Processing** - Efficient video processing pipeline
- 📱 **Mobile Ready** - Responsive design that works on all devices
- 🌟 **Real-time Progress** - Live updates with beautiful progress indicators

## 🛠️ Installation & Setup

### Prerequisites

- Python 3.8+ (tested with Python 3.13)
- FFmpeg (for video processing)
- LM Studio with `llama-3.2-8x3b-moe-dark-champion-instruct-uncensored-abliterated-18.4b` model

### 1. Clone and Setup Python Environment

```bash
git clone https://github.com/SamurAIGPT/AI-Youtube-Shorts-Generator.git
cd AI-Youtube-Shorts-Generator

# Create virtual environment
python -m venv venv

# Activate virtual environment (Windows)
venv\Scripts\activate

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configure LM Studio

1. **Install LM Studio** from [https://lmstudio.ai/](https://lmstudio.ai/)
2. **Download the model**: `llama-3.2-8x3b-moe-dark-champion-instruct-uncensored-abliterated-18.4b`
3. **Start the local server** (usually runs on `http://localhost:1234`)
4. **Verify configuration** in `.env` file:

```env
LM_STUDIO_BASE_URL=http://localhost:1234
LM_STUDIO_MODEL=llama-3.2-8x3b-moe-dark-champion-instruct-uncensored-abliterated-18.4b
```

### 3. Test Your Setup

```bash
# Test LM Studio connectivity
python test_lm_studio.py
```

## 🚀 Usage

### Quick Start - HTML Version (Recommended)

```bash
# Run the beautiful HTML app (single command)
start_html_app.bat
```

**Access the app**: http://localhost:8000

### Alternative - React Version

```bash
# Run both servers separately
start_servers.bat
```

**Access the app**: http://localhost:3000

### Manual Start

**Option 1 - HTML Frontend (Simpler):**
```bash
# Activate virtual environment
venv\Scripts\activate

# Start backend with HTML frontend
python backend/app_simple.py
```

**Option 2 - React Frontend:**
```bash
# Terminal 1 - Backend
venv\Scripts\activate
python backend/app_simple.py

# Terminal 2 - React Frontend
cd frontend
npm run dev
```

## 🎯 How It Works

1. **Input YouTube URL** - Paste any YouTube video URL
2. **Download & Extract** - Video is downloaded and audio extracted
3. **Transcription** - Audio transcribed using OpenAI Whisper
4. **AI Analysis** - LM Studio analyzes transcript for highlights
5. **Video Creation** - Automatically crops and creates short video
6. **Download** - Get your AI-generated short ready for upload

## 🎨 Frontend Options

### HTML/CSS/JS Frontend (Recommended)
- **Beautiful animations** with CSS gradients and effects
- **Real-time progress** with smooth transitions
- **Responsive design** that works on all devices
- **No build process** - just open and use
- **Celebration effects** when processing completes

### React/Next.js Frontend (Advanced)
- **Modern React components** with TypeScript
- **Framer Motion animations** for smooth interactions
- **Tailwind CSS** for utility-first styling
- **Component-based architecture** for maintainability

## 📁 Project Structure

```
AI-Youtube-Shorts-Generator/
├── backend/                 # FastAPI backend server
│   ├── app_simple.py       # Main backend application
│   └── app.py              # Full backend (with face detection)
├── frontend_html/          # Beautiful HTML/CSS/JS frontend
│   ├── index.html         # Main HTML file
│   ├── style.css          # Stunning CSS with animations
│   └── script.js          # Interactive JavaScript
├── frontend/               # React/Next.js frontend (alternative)
├── Components/             # Core processing modules
│   ├── YoutubeDownloader.py
│   ├── Transcription.py
│   ├── LanguageTasks.py   # LM Studio integration
│   ├── Edit.py
│   └── FaceCrop.py
├── .env                   # Environment configuration
├── requirements.txt       # Python dependencies
├── test_lm_studio.py     # LM Studio test script
├── start_html_app.bat    # HTML app startup script
└── start_servers.bat     # React app startup script
```

## 🔧 Configuration

### Environment Variables (.env)

```env
# LM Studio Configuration
LM_STUDIO_BASE_URL=http://localhost:1234
LM_STUDIO_MODEL=llama-3.2-8x3b-moe-dark-champion-instruct-uncensored-abliterated-18.4b
```

## 🐛 Troubleshooting

### Common Issues

1. **LM Studio Connection Failed**
   - Ensure LM Studio is running
   - Check the model is loaded
   - Verify the port (default: 1234)

2. **Python Dependencies Issues**
   - Use Python 3.8-3.12 for better compatibility
   - Install `pyaudioop` if needed: `pip install pyaudioop`

3. **FFmpeg Not Found**
   - Install FFmpeg: https://ffmpeg.org/download.html
   - Add to system PATH

4. **Frontend Not Loading**
   - Try the HTML version: `start_html_app.bat`
   - Check if backend is running on port 8000

## 🎉 What's New

### Beautiful HTML Frontend
- **Stunning gradient backgrounds** with animated colors
- **Glassmorphism effects** with backdrop blur
- **Interactive animations** and hover effects
- **Real-time progress tracking** with smooth transitions
- **Celebration particles** when processing completes
- **Mouse trail effects** for enhanced interactivity
- **Responsive design** that looks great on all devices

### Enhanced User Experience
- **One-click startup** with batch files
- **Real-time status updates** during processing
- **Beautiful error handling** with helpful messages
- **Smooth animations** throughout the interface
- **Mobile-optimized** design for all screen sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Original project by [SamurAIGPT](https://github.com/SamurAIGPT)
- LM Studio for local LLM capabilities
- OpenAI Whisper for transcription
- FastAPI for the modern backend
- Beautiful CSS animations inspired by modern web design

---

**Made with ❤️ for the AI community**

*Now featuring a stunning HTML frontend with beautiful animations and effects!*
