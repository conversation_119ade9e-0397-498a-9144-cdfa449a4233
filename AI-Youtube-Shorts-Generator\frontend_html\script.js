// Configuration
const API_BASE_URL = 'http://localhost:8000';

// DOM Elements
const videoForm = document.getElementById('video-form');
const youtubeUrlInput = document.getElementById('youtube-url');
const inputForm = document.getElementById('input-form');
const processingStatus = document.getElementById('processing-status');
const successStatus = document.getElementById('success-status');
const errorStatus = document.getElementById('error-status');
const statusMessage = document.getElementById('status-message');
const progressFill = document.getElementById('progress-fill');
const progressText = document.getElementById('progress-text');
const errorMessage = document.getElementById('error-message');
const downloadBtn = document.getElementById('download-btn');
const resetBtn = document.getElementById('reset-btn');
const retryBtn = document.getElementById('retry-btn');

// State
let currentJobId = null;
let pollingInterval = null;

// Event Listeners
videoForm.addEventListener('submit', handleFormSubmit);
resetBtn.addEventListener('click', resetForm);
retryBtn.addEventListener('click', resetForm);
downloadBtn.addEventListener('click', handleDownload);

// Form submission handler
async function handleFormSubmit(e) {
    e.preventDefault();
    
    const youtubeUrl = youtubeUrlInput.value.trim();
    if (!youtubeUrl) return;

    try {
        showProcessingStatus();
        
        const response = await fetch(`${API_BASE_URL}/api/process-video`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ youtube_url: youtubeUrl }),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        currentJobId = result.job_id;
        
        // Start polling for job status
        startPolling();
        
    } catch (error) {
        console.error('Error starting video processing:', error);
        showErrorStatus('Failed to start processing. Please check if the backend server is running.');
    }
}

// Start polling for job status
function startPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
    
    pollingInterval = setInterval(async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/api/job/${currentJobId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const jobStatus = await response.json();
            updateProgress(jobStatus);
            
            if (jobStatus.status === 'completed') {
                clearInterval(pollingInterval);
                showSuccessStatus(jobStatus.result_url);
            } else if (jobStatus.status === 'failed') {
                clearInterval(pollingInterval);
                showErrorStatus(jobStatus.error || 'Processing failed');
            }
            
        } catch (error) {
            console.error('Error polling job status:', error);
            clearInterval(pollingInterval);
            showErrorStatus('Lost connection to server');
        }
    }, 2000); // Poll every 2 seconds
}

// Update progress display
function updateProgress(jobStatus) {
    const progress = jobStatus.progress || 0;
    const message = jobStatus.message || 'Processing...';
    
    progressFill.style.width = `${progress}%`;
    progressText.textContent = `${progress}% complete`;
    statusMessage.textContent = message;
    
    // Add some visual feedback
    if (progress > 0) {
        progressFill.style.background = `linear-gradient(90deg, #667eea ${progress}%, #764ba2 100%)`;
    }
}

// Show processing status
function showProcessingStatus() {
    hideAllSections();
    processingStatus.classList.remove('hidden');
    
    // Reset progress
    progressFill.style.width = '0%';
    progressText.textContent = '0% complete';
    statusMessage.textContent = 'Starting processing...';
    
    // Add animation class
    processingStatus.style.animation = 'fadeIn 0.5s ease-out';
}

// Show success status
function showSuccessStatus(resultUrl) {
    hideAllSections();
    successStatus.classList.remove('hidden');
    
    // Store result URL for download
    downloadBtn.dataset.resultUrl = resultUrl;
    
    // Add animation class
    successStatus.style.animation = 'fadeIn 0.5s ease-out';
    
    // Add celebration effect
    createCelebrationEffect();
}

// Show error status
function showErrorStatus(error) {
    hideAllSections();
    errorStatus.classList.remove('hidden');
    errorMessage.textContent = error;
    
    // Add animation class
    errorStatus.style.animation = 'fadeIn 0.5s ease-out';
}

// Hide all status sections
function hideAllSections() {
    inputForm.classList.add('hidden');
    processingStatus.classList.add('hidden');
    successStatus.classList.add('hidden');
    errorStatus.classList.add('hidden');

    // Re-initialize Lucide icons after DOM changes
    setTimeout(() => {
        lucide.createIcons();
    }, 100);
}

// Reset form to initial state
function resetForm() {
    // Clear polling
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
    
    // Reset state
    currentJobId = null;
    youtubeUrlInput.value = '';
    
    // Show input form
    hideAllSections();
    inputForm.classList.remove('hidden');
    
    // Add animation class
    inputForm.style.animation = 'fadeIn 0.5s ease-out';
    
    // Focus on input
    setTimeout(() => {
        youtubeUrlInput.focus();
    }, 100);
}

// Handle download
function handleDownload() {
    const resultUrl = downloadBtn.dataset.resultUrl;
    if (resultUrl) {
        // Open download in new tab
        window.open(`${API_BASE_URL}${resultUrl}`, '_blank');
        
        // Add download animation
        downloadBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            downloadBtn.style.transform = 'scale(1)';
        }, 150);
    }
}

// Create celebration effect
function createCelebrationEffect() {
    // Create floating particles
    for (let i = 0; i < 20; i++) {
        createParticle();
    }
}

// Create individual particle
function createParticle() {
    const particle = document.createElement('div');
    particle.style.cssText = `
        position: fixed;
        width: 8px;
        height: 8px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        left: ${Math.random() * window.innerWidth}px;
        top: ${window.innerHeight}px;
        animation: float-up 3s ease-out forwards;
    `;
    
    document.body.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 3000);
}

// Add CSS for particle animation
const style = document.createElement('style');
style.textContent = `
    @keyframes float-up {
        0% {
            transform: translateY(0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(-100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// URL validation
youtubeUrlInput.addEventListener('input', function() {
    const url = this.value;
    const submitBtn = videoForm.querySelector('button[type="submit"]');
    const urlStatus = document.getElementById('url-status');

    if (url && isValidYouTubeUrl(url)) {
        submitBtn.disabled = false;
        this.style.borderColor = 'hsl(142 76% 36%)';
        urlStatus.classList.remove('hidden');
    } else if (url) {
        submitBtn.disabled = true;
        this.style.borderColor = 'hsl(0 84% 60%)';
        urlStatus.classList.add('hidden');
    } else {
        submitBtn.disabled = true;
        this.style.borderColor = 'hsl(var(--border))';
        urlStatus.classList.add('hidden');
    }

    // Re-initialize icons
    lucide.createIcons();
});

// YouTube URL validation
function isValidYouTubeUrl(url) {
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)[a-zA-Z0-9_-]{11}/;
    return youtubeRegex.test(url);
}

// Health check on page load
window.addEventListener('load', async function() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/health`);
        if (response.ok) {
            console.log('✅ Backend server is running');
        } else {
            console.warn('⚠️ Backend server responded with error');
        }
    } catch (error) {
        console.error('❌ Cannot connect to backend server:', error);
        // You could show a warning message to the user here
    }
});

// Add some interactive effects
document.addEventListener('mousemove', function(e) {
    // Create subtle mouse trail effect
    if (Math.random() > 0.95) { // Only occasionally create particles
        const trail = document.createElement('div');
        trail.style.cssText = `
            position: fixed;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1;
            left: ${e.clientX}px;
            top: ${e.clientY}px;
            animation: fade-out 1s ease-out forwards;
        `;
        
        document.body.appendChild(trail);
        
        setTimeout(() => {
            if (trail.parentNode) {
                trail.parentNode.removeChild(trail);
            }
        }, 1000);
    }
});

// Add fade-out animation for mouse trail
const trailStyle = document.createElement('style');
trailStyle.textContent = `
    @keyframes fade-out {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0);
        }
    }
`;
document.head.appendChild(trailStyle);
