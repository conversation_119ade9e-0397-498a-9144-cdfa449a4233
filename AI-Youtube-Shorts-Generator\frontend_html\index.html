<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI YouTube Shorts Generator</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-background text-foreground">
    <!-- Main Container -->
    <div class="min-h-screen bg-gradient-to-br from-background to-muted/20">
        <!-- Navigation -->
        <nav class="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container mx-auto px-4 h-16 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                        <i data-lucide="scissors" class="h-4 w-4 text-primary-foreground"></i>
                    </div>
                    <span class="font-semibold text-lg">AI Shorts Generator</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-muted-foreground">
                        <div class="h-2 w-2 rounded-full bg-green-500"></div>
                        <span>LM Studio Connected</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="container mx-auto px-4 py-16">
            <div class="text-center space-y-6 max-w-3xl mx-auto">
                <div class="inline-flex items-center rounded-full border border-border px-3 py-1 text-sm bg-muted/50">
                    <i data-lucide="sparkles" class="mr-2 h-4 w-4"></i>
                    Powered by Local LLM
                </div>
                <h1 class="text-4xl font-bold tracking-tight sm:text-6xl bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                    Transform Videos into
                    <span class="text-primary">AI Shorts</span>
                </h1>
                <p class="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                    Automatically extract the most engaging moments from YouTube videos using advanced AI analysis and create perfect shorts for social media.
                </p>
            </div>
        </section>

        <!-- Features Grid -->
        <section class="container mx-auto px-4 pb-16">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
                <div class="group relative overflow-hidden rounded-xl border border-border bg-card p-6 hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="h-10 w-10 rounded-lg bg-red-500/10 flex items-center justify-center">
                            <i data-lucide="youtube" class="h-5 w-5 text-red-500"></i>
                        </div>
                        <h3 class="font-semibold text-lg">YouTube Integration</h3>
                    </div>
                    <p class="text-muted-foreground leading-relaxed">
                        Seamlessly download and process videos from any YouTube URL with automatic format detection.
                    </p>
                </div>

                <div class="group relative overflow-hidden rounded-xl border border-border bg-card p-6 hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                            <i data-lucide="zap" class="h-5 w-5 text-primary"></i>
                        </div>
                        <h3 class="font-semibold text-lg">LM Studio Powered</h3>
                    </div>
                    <p class="text-muted-foreground leading-relaxed">
                        Advanced AI analysis using your local LM Studio instance for privacy-focused processing.
                    </p>
                </div>

                <div class="group relative overflow-hidden rounded-xl border border-border bg-card p-6 hover:shadow-lg transition-all duration-300">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center">
                            <i data-lucide="sparkles" class="h-5 w-5 text-green-500"></i>
                        </div>
                        <h3 class="font-semibold text-lg">Smart Highlights</h3>
                    </div>
                    <p class="text-muted-foreground leading-relaxed">
                        Automatically detect and extract the most engaging moments using AI content analysis.
                    </p>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <main class="container mx-auto px-4 pb-16">
            <div class="max-w-2xl mx-auto">
                <div class="rounded-xl border border-border bg-card shadow-lg">
                    <!-- Input Form -->
                    <div id="input-form" class="p-8">
                        <div class="text-center mb-8">
                            <h2 class="text-2xl font-semibold mb-2">Create Your AI Short</h2>
                            <p class="text-muted-foreground">Paste a YouTube URL to get started</p>
                        </div>

                        <form id="video-form" class="space-y-6">
                            <div class="space-y-2">
                                <label for="youtube-url" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                    YouTube Video URL
                                </label>
                                <div class="relative">
                                    <i data-lucide="youtube" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"></i>
                                    <input
                                        type="url"
                                        id="youtube-url"
                                        class="flex h-12 w-full rounded-md border border-input bg-background px-10 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        placeholder="https://www.youtube.com/watch?v=..."
                                        required
                                    >
                                    <div id="url-status" class="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 hidden">
                                        <i data-lucide="check" class="h-4 w-4 text-green-500"></i>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-12 px-6 py-2 w-full">
                                <i data-lucide="play" class="mr-2 h-4 w-4"></i>
                                Generate AI Short
                            </button>
                        </form>
                    </div>

                    <!-- Processing Status -->
                    <div id="processing-status" class="p-8 text-center space-y-6 hidden">
                        <div class="flex justify-center">
                            <div class="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                                <i data-lucide="loader-2" class="h-8 w-8 text-primary animate-spin"></i>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h3 class="text-xl font-semibold">Processing Your Video</h3>
                            <p id="status-message" class="text-muted-foreground">Starting processing...</p>
                        </div>

                        <div class="space-y-3">
                            <div class="w-full bg-secondary rounded-full h-2">
                                <div id="progress-fill" class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <span id="progress-text" class="text-sm text-muted-foreground">0% complete</span>
                        </div>
                    </div>

                    <!-- Success Status -->
                    <div id="success-status" class="p-8 text-center space-y-6 hidden">
                        <div class="flex justify-center">
                            <div class="h-16 w-16 rounded-full bg-green-500/10 flex items-center justify-center">
                                <i data-lucide="check-circle" class="h-8 w-8 text-green-500"></i>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h3 class="text-xl font-semibold">Short Generated Successfully!</h3>
                            <p class="text-muted-foreground">Your AI-generated short is ready for download</p>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            <button id="download-btn" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-green-600 text-white hover:bg-green-700 h-10 px-4 py-2">
                                <i data-lucide="download" class="mr-2 h-4 w-4"></i>
                                Download Short
                            </button>
                            <button id="reset-btn" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                                Create Another
                            </button>
                        </div>
                    </div>

                    <!-- Error Status -->
                    <div id="error-status" class="p-8 text-center space-y-6 hidden">
                        <div class="flex justify-center">
                            <div class="h-16 w-16 rounded-full bg-destructive/10 flex items-center justify-center">
                                <i data-lucide="alert-circle" class="h-8 w-8 text-destructive"></i>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <h3 class="text-xl font-semibold">Processing Failed</h3>
                            <p id="error-message" class="text-muted-foreground">An error occurred during processing</p>
                        </div>

                        <button id="retry-btn" class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                            Try Again
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div class="container mx-auto px-4 py-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>Powered by LM Studio</span>
                        <span>•</span>
                        <span>Built with ❤️ for the AI community</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="/api" class="text-sm text-muted-foreground hover:text-foreground transition-colors">API</a>
                        <a href="/docs" class="text-sm text-muted-foreground hover:text-foreground transition-colors">Docs</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
    <script src="script.js"></script>
</body>
</html>
