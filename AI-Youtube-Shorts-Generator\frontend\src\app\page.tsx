"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { motion, AnimatePresence } from "framer-motion";
import {
  Youtube,
  Sparkles,
  Download,
  Loader2,
  CheckCircle,
  AlertCircle,
  Play,
  Scissors,
  Zap
} from "lucide-react";

interface JobStatus {
  job_id: string;
  status: string;
  progress: number;
  message: string;
  result_url?: string;
  error?: string;
}

export default function Home() {
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [jobId, setJobId] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<JobStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  const API_BASE_URL = "http://localhost:8000";

  // Poll job status
  useEffect(() => {
    if (jobId && isProcessing) {
      const interval = setInterval(async () => {
        try {
          const response = await fetch(`${API_BASE_URL}/job/${jobId}`);
          const status: JobStatus = await response.json();
          setJobStatus(status);

          if (status.status === "completed" || status.status === "failed") {
            setIsProcessing(false);
            clearInterval(interval);
          }
        } catch (err) {
          console.error("Error polling job status:", err);
        }
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [jobId, isProcessing]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!youtubeUrl.trim()) return;

    setIsProcessing(true);
    setError(null);
    setJobStatus(null);

    try {
      const response = await fetch(`${API_BASE_URL}/process-video`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ youtube_url: youtubeUrl }),
      });

      if (!response.ok) {
        throw new Error("Failed to start processing");
      }

      const result = await response.json();
      setJobId(result.job_id);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
      setIsProcessing(false);
    }
  };

  const handleDownload = () => {
    if (jobStatus?.result_url) {
      window.open(`${API_BASE_URL}${jobStatus.result_url}`, "_blank");
    }
  };

  const resetForm = () => {
    setYoutubeUrl("");
    setIsProcessing(false);
    setJobId(null);
    setJobStatus(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
              <Scissors className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              AI Shorts Generator
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Transform long YouTube videos into engaging shorts using AI-powered highlight detection with LM Studio
          </p>
        </motion.div>

        {/* Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid md:grid-cols-3 gap-6 mb-12"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-4">
              <Youtube className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">YouTube Integration</h3>
            <p className="text-gray-300">Seamlessly download and process videos from YouTube URLs</p>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mb-4">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">LM Studio Powered</h3>
            <p className="text-gray-300">Advanced AI analysis using your local LM Studio instance</p>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-4">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Smart Highlights</h3>
            <p className="text-gray-300">Automatically detect the most engaging moments</p>
          </div>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="max-w-2xl mx-auto"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20">
            {!isProcessing && !jobStatus ? (
              /* Input Form */
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="youtube-url" className="block text-lg font-medium text-white mb-3">
                    YouTube Video URL
                  </label>
                  <div className="relative">
                    <Youtube className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      id="youtube-url"
                      type="url"
                      placeholder="https://www.youtube.com/watch?v=..."
                      value={youtubeUrl}
                      onChange={(e) => setYoutubeUrl(e.target.value)}
                      className="pl-12 h-14 text-lg bg-white/5 border-white/20 text-white placeholder:text-gray-400"
                      required
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  size="lg"
                  className="w-full h-14 text-lg bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0"
                  disabled={!youtubeUrl.trim()}
                >
                  <Play className="w-5 h-5 mr-2" />
                  Generate AI Short
                </Button>
              </form>
            ) : (
              /* Processing Status */
              <div className="text-center space-y-6">
                <AnimatePresence mode="wait">
                  {isProcessing && (
                    <motion.div
                      key="processing"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="space-y-4"
                    >
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto">
                        <Loader2 className="w-8 h-8 text-white animate-spin" />
                      </div>
                      <h3 className="text-2xl font-semibold text-white">Processing Your Video</h3>
                      <p className="text-gray-300">{jobStatus?.message || "Starting processing..."}</p>

                      <div className="space-y-2">
                        <Progress value={jobStatus?.progress || 0} className="h-3" />
                        <p className="text-sm text-gray-400">{jobStatus?.progress || 0}% complete</p>
                      </div>
                    </motion.div>
                  )}

                  {jobStatus?.status === "completed" && (
                    <motion.div
                      key="completed"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="space-y-6"
                    >
                      <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto">
                        <CheckCircle className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-semibold text-white">Short Generated Successfully!</h3>
                      <p className="text-gray-300">Your AI-generated short is ready for download</p>

                      <div className="flex gap-4 justify-center">
                        <Button
                          onClick={handleDownload}
                          size="lg"
                          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white"
                        >
                          <Download className="w-5 h-5 mr-2" />
                          Download Short
                        </Button>
                        <Button
                          onClick={resetForm}
                          size="lg"
                          variant="outline"
                          className="border-white/20 text-white hover:bg-white/10"
                        >
                          Create Another
                        </Button>
                      </div>
                    </motion.div>
                  )}

                  {jobStatus?.status === "failed" && (
                    <motion.div
                      key="failed"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="space-y-6"
                    >
                      <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto">
                        <AlertCircle className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-semibold text-white">Processing Failed</h3>
                      <p className="text-gray-300">{jobStatus?.error || "An error occurred during processing"}</p>

                      <Button
                        onClick={resetForm}
                        size="lg"
                        className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
                      >
                        Try Again
                      </Button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}

            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 p-4 bg-red-500/20 border border-red-500/30 rounded-lg"
              >
                <p className="text-red-200">{error}</p>
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}