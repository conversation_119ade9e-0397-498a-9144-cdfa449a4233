import whisper
import torch

def transcribeAudio(audio_path):
    try:
        print("Transcribing audio...")
        Device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {Device}")
        model = whisper.load_model("base.en", device=Device)
        print("Model loaded")
        result = model.transcribe(audio_path, language="en")

        # Extract segments with text, start, and end times
        extracted_texts = []
        for segment in result["segments"]:
            extracted_texts.append([segment["text"], segment["start"], segment["end"]])

        return extracted_texts
    except Exception as e:
        print("Transcription Error:", e)
        return []

if __name__ == "__main__":
    audio_path = "audio.wav"
    transcriptions = transcribeAudio(audio_path)
    print("Done")
    TransText = ""

    for text, start, end in transcriptions:
        TransText += (f"{start} - {end}: {text}")
    print(TransText)